// background.js
import { checkUrl, loadStatic } from "./utils/utils.js";
import { FAW_REG } from "./utils/faw.js";
const initRules = async () => {
  const rules = await loadStatic("rules/permission.json");
  const { enable } = await chrome.storage.local.get("enable");

  chrome.declarativeNetRequest.getDynamicRules((currentRules) => {
    // 提取所有动态规则的 ID
    const ruleIds = currentRules.map((rule) => rule.id);
    const addRules = rules.filter((rule) => !ruleIds.includes(rule.id));

    if (enable === "true") {
      chrome.declarativeNetRequest.updateDynamicRules({ addRules }, () => {
        console.log(chrome.runtime.lastError);
      });
    } else {
      chrome.declarativeNetRequest.updateDynamicRules(
        { removeRuleIds: ruleIds },
        () => {
          console.log(chrome.runtime.lastError);
        }
      );
    }
  });
};

chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === "complete" && checkUrl(FAW_REG, tab?.url)) {
    initRules();
  }
});
