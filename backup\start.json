{"requestId": "26f7d490-83ff-446c-95dc-b32c9fb4674e", "code": "", "message": "success", "success": true, "traceId": null, "empty": false, "meta": null, "data": {"applicationId": 19440, "projectId": 7811, "taskId": "dayu-19440-74799-3344145", "applicationName": null, "buildId": 3344145, "startTime": null, "endTime": null, "status": 1, "creator": "裴忠莉(1)", "buildCnt": null, "jobName": "de-0505_app_buc_fe-daily", "queueReference": null, "stageList": [], "environmentType": null, "customPipeline": false, "alias": null, "message": null, "gitBranch": null, "pipelineId": null, "isCurrentBuild": null, "imageTag": null, "rollBackToBuildId": null, "environmentDetailId": null, "buildType": null, "triggerType": null, "commitId": null, "tag": null, "supportedTriggerTypes": null, "ossVersion": null, "branchMode": null, "deployImageAddress": null, "runtime": null, "sourceFlag": null, "startPipelineFlag": null}}