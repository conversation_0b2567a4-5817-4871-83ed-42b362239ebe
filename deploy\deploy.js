import {
  formatDate,
  LoadingSpinner,
  TimeIntervalUpdater,
} from "../utils/utils.js";
import {
  STATUS_MAP,
  getCode,
  DOING,
  PREPARE,
  DONE,
  ERROR,
  ABORT,
  JUMP,
} from "../utils/faw.js";
import { $post } from "../utils/fetch.js";

const result = document.querySelector("#result");
const deployBtn = document.querySelector("#deployBtn");
const abortBtn = document.querySelector("#abortBtn");
const codeInput = document.querySelector("#code");
let checkTimer = null;
let loadingTimer = null;
let calculateTimer = null;

// 部署
deployBtn.addEventListener("click", function () {
  const code = codeInput.value.trim();
  if (!code) {
    return alert("请输入用例号");
  }
  deployBtn.setAttribute("data-code", code);
  startPipeline(code);
});

// 终止
abortBtn.addEventListener("click", function () {
  const buildId = abortBtn.getAttribute("data-buildId");
  const code = deployBtn.getAttribute("data-code");
  if (buildId) {
    abortPipeline(buildId, code);
  }
});

// 展示进度
const startCalculateProcess = (buildId, startTime) => {
  abortBtn.style.display = "block";
  abortBtn.setAttribute("data-buildId", buildId);

  if (!loadingTimer) {
    loadingTimer = new LoadingSpinner("#loadingStr");
    loadingTimer.start();
  }
  if (!calculateTimer && startTime) {
    calculateTimer = new TimeIntervalUpdater("#timeIntervalStr");
    calculateTimer.start(startTime);
  }

  checkTimer = window.setTimeout(() => checkProgress(buildId), 20 * 1000);
};

// 重置状态
const resetStatus = () => {
  clearTimeout(checkTimer);
  checkTimer = null;
  loadingTimer.end();
  calculateTimer.end();
  loadingTimer = null;
  calculateTimer = null;

  abortBtn.style.display = "none";
};

const getSuffixStr = (status) => {
  switch (status) {
    case DOING:
      return '<span id="loadingStr"></span>';
    case DONE:
      return `😊`;
    case ERROR:
      return `😟`;
    case ABORT:
      return `😶`;
    case JUMP:
      return `✈`;

    default:
      return "";
  }
};

// 展示状态
const renderDeployStatus = (data) => {
  const timeIntervalDom = '<span id="timeIntervalStr">-</span>';
  const timeStr = data.endTime ? formatDate(data.endTime) : timeIntervalDom;

  result.innerHTML = `
      <p>流水线id【${data.buildId}】： ${STATUS_MAP[data.status]} | ${
    data.creator
  } | ${timeStr}</p><br/>
      <ul>
        ${data.stageList
          .map((i, index, arr) => {
            return (i.subStages?.length ? i.subStages : [i]).map((j) => {
              return `
                <li>
                  [${j.stageName}] ${STATUS_MAP[j.status]} ${getSuffixStr(
                j.status
              )}</i><br/>
                  开始时间：${formatDate(j.startTime)} <br/>
                  结束时间：${formatDate(j.endTime)} <br/>
                  ${
                    index < arr.length - 1
                      ? "----------------------------------------------------"
                      : ""
                  }
                </li>
              `;
            });
          })
          .flat()
          .join("")}
      </ul>
    `;
};

// 轮询进度
const checkProgress = async (buildId) => {
  if (!buildId) {
    return;
  }
  const { success, data } = await $post(
    "http://***********:510/local-faw/api/buildService/api/app/build/detail",
    {
      buildId,
    }
  );

  if (success) {
    renderDeployStatus(data);
    const isDoing = [PREPARE, DOING].includes(data.status);
    if (isDoing) {
      startCalculateProcess(buildId, data.stageList?.[0]?.startTime);
    } else {
      resetStatus();
    }
  }
};

// 调部署接口
const startPipeline = async (code) => {
  const {
    success,
    data,
    message,
    code: errorCode,
  } = await $post("http://***********:510/local-faw/api/buildService/api/app/build/startPipeline", {
    applicationCode: getCode(code),
    environmentType: "daily",
  });

  if (success) {
    result.innerHTML = `开始构建，构建ID：${data.buildId}`;
    checkProgress(data.buildId);
  } else if (errorCode === "409") {
    const buildId = message.match(/(?<=id:\s)\d+/)?.[0];
    result.innerHTML = `构建失败，当前正在构建的ID：${buildId}`;
    checkProgress(buildId);
  }
};

// 调终止接口
const abortPipeline = async (buildId, code) => {
  const { success, data } = await $post(
    "http://***********:510/local-faw/api/buildService/api/app/build/abortPipeline",
    {
      buildId,
      applicationCode: getCode(code),
      environmentType: "daily",
    }
  );
  if (success) {
    result.innerHTML = `<p>流水线【${buildId}】已终止</p>`;
  }
};
