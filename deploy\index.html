<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>部署环境</title>
    <style>
      * {
        margin: 0;
        padding: 0;
      }

      .input-group {
        display: flex;
        align-items: center;
      }

      input {
        border-radius: 3px;
        height: 32px;
        padding: 0 6px;
        box-sizing: border-box;
        margin: 12px 0;
      }

      button {
        display: block;
        border-color: transparent;
        padding: 2px 6px;
        border-radius: 3px;
        cursor: pointer;
        margin-left: 3px;
      }

      #deployBtn {
        background-color: #0078d4;
        color: #fff;
      }

      #abortBtn {
        background-color: #f00;
        color: #fff;
        display: none;
      }
    </style>
  </head>
  <body>
    <h1>部署环境</h1>
    <div class="input-group">
      <input type="text" id="code" placeholder="请输入部署环境的用例" />
      <button id="deployBtn">部署</button>
      <button id="abortBtn">终止</button>
    </div>
    <div id="result"></div>
    <script type="module" src="./deploy.js"></script>
  </body>
</html>
