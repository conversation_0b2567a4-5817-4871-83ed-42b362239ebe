(()=>{function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function r(e){for(var r=1;r<arguments.length;r++){var o=null!=arguments[r]?arguments[r]:{};r%2?t(Object(o),!0).forEach(function(t){n(e,t,o[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):t(Object(o)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))})}return e}function n(t,r,n){return(r=function(t){var r=function(t){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=e(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==e(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n,t}console.log("Background script loaded"),chrome.runtime.onInstalled.addListener(function(){console.log("Extension installed"),chrome.storage.local.get("enable",function(e){void 0===e.enable&&chrome.storage.local.set({enable:"false"})})}),chrome.runtime.onMessage.addListener(function(e,t,n){if("getStatus"===e.type)return chrome.storage.local.get("enable",function(e){n({enabled:"true"===e.enable})}),!0;if("FETCH_USER_LIST"===e.type){var o=e.payload,a=o.jwt,c=o.search;return fetch("http://***********:410/api-dev/qfc-business-bcc/v2/user/selector/getUserWithRecent",{method:"POST",headers:{Authorization:a,"Content-Type":"application/json"},body:JSON.stringify({search:c,pageSize:6,requestDataObject:{},requestDataType:"NORMAL",rootCode:"CUSTOM_TASK"})}).then(function(e){if(!e.ok)throw new Error("API请求失败: ".concat(e.status));return e.json()}).then(function(e){if(e&&Array.isArray(e.data)){var t=e.data.map(function(e){return r(r({},e),{},{value:e.name+"(".concat(e.loginName,")"),label:e.name+"(".concat(e.loginName,")")})});n({data:t})}else n({error:"获取用户列表失败，数据格式不正确"})}).catch(function(e){console.error("获取用户列表失败:",e),n({error:"获取用户列表失败: "+e.message})}),!0}})})();
//# sourceMappingURL=background.js.map