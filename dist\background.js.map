{"version": 3, "file": "background.js", "mappings": "krCACAA,QAAQC,IAAI,4BAGZC,OAAOC,QAAQC,YAAYC,YAAY,WACrCL,QAAQC,IAAI,uBAGZC,OAAOI,QAAQC,MAAMC,IAAI,SAAU,SAACC,QACZC,IAAlBD,EAAOE,QACTT,OAAOI,QAAQC,MAAMK,IAAI,CAAED,OAAQ,SAEvC,EACF,GAGAT,OAAOC,QAAQU,UAAUR,YAAY,SAACS,EAASC,EAAQC,GACrD,GAAqB,cAAjBF,EAAQG,KAIV,OAHAf,OAAOI,QAAQC,MAAMC,IAAI,SAAU,SAACC,GAClCO,EAAa,CAAEE,QAA2B,SAAlBT,EAAOE,QACjC,IACO,EAIT,GAAqB,oBAAjBG,EAAQG,KAA4B,CACtC,IAAAE,EAAwBL,EAAQM,QAAxBC,EAAGF,EAAHE,IAAKC,EAAMH,EAANG,OAwCb,OAtCAC,MAAM,qFAAsF,CAC1FC,OAAQ,OACRC,QAAS,CACP,cAAiBJ,EACjB,eAAgB,oBAElBK,KAAMC,KAAKC,UAAU,CACnBN,OAAQA,EACRO,SAAU,EACVC,kBAAmB,CAAC,EACpBC,gBAAiB,SACjBC,SAAU,kBAGbC,KAAK,SAAAC,GACJ,IAAKA,EAASC,GACZ,MAAM,IAAIC,MAAM,YAADC,OAAaH,EAASI,SAEvC,OAAOJ,EAASK,MAClB,GACCN,KAAK,SAAAO,GACJ,GAAIA,GAAQC,MAAMC,QAAQF,EAAKA,MAAO,CAEpC,IAAMG,EAAcH,EAAKA,KAAKI,IAAI,SAAAC,GAAI,OAAAC,EAAAA,EAAA,GACjCD,GAAI,IACPE,MAAOF,EAAKG,KAAO,IAAHX,OAAOQ,EAAKI,UAAS,KACrCC,MAAOL,EAAKG,KAAO,IAAHX,OAAOQ,EAAKI,UAAS,MAAG,GAE1CjC,EAAa,CAAEwB,KAAMG,GACvB,MACE3B,EAAa,CAAEmC,MAAO,oBAE1B,GAAE,MACK,SAAAA,GACLnD,QAAQmD,MAAM,YAAaA,GAC3BnC,EAAa,CAAEmC,MAAO,aAAeA,EAAMrC,SAC7C,IAEO,CACT,CACF,E", "sources": ["webpack://faw-mock/./src/background.js"], "sourcesContent": ["// 后台脚本\nconsole.log('Background script loaded');\n\n// 初始化扩展\nchrome.runtime.onInstalled.addListener(() => {\n  console.log('Extension installed');\n  \n  // 设置默认值\n  chrome.storage.local.get('enable', (result) => {\n    if (result.enable === undefined) {\n      chrome.storage.local.set({ enable: 'false' });\n    }\n  });\n});\n\n// 监听消息\nchrome.runtime.onMessage.addListener((message, sender, sendResponse) => {\n  if (message.type === 'getStatus') {\n    chrome.storage.local.get('enable', (result) => {\n      sendResponse({ enabled: result.enable === 'true' });\n    });\n    return true; // 异步响应\n  }\n  \n  // 处理获取用户列表的请求\n  if (message.type === 'FETCH_USER_LIST') {\n    const { jwt, search } = message.payload;\n    \n    fetch('http://***********:410/api-dev/qfc-business-bcc/v2/user/selector/getUserWithRecent', {\n      method: 'POST',\n      headers: {\n        'Authorization': jwt,\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        search: search,\n        pageSize: 6,\n        requestDataObject: {},\n        requestDataType: \"NORMAL\",\n        rootCode: \"CUSTOM_TASK\"\n      })\n    })\n    .then(response => {\n      if (!response.ok) {\n        throw new Error(`API请求失败: ${response.status}`);\n      }\n      return response.json();\n    })\n    .then(data => {\n      if (data && Array.isArray(data.data)) {\n        // 转换API返回的数据格式\n        const userOptions = data.data.map(user => ({\n          ...user,\n          value: user.name + `(${user.loginName})`,\n          label: user.name + `(${user.loginName})`\n        }));\n        sendResponse({ data: userOptions });\n      } else {\n        sendResponse({ error: '获取用户列表失败，数据格式不正确' });\n      }\n    })\n    .catch(error => {\n      console.error('获取用户列表失败:', error);\n      sendResponse({ error: '获取用户列表失败: ' + error.message });\n    });\n    \n    return true; // 异步响应\n  }\n});\n"], "names": ["console", "log", "chrome", "runtime", "onInstalled", "addListener", "storage", "local", "get", "result", "undefined", "enable", "set", "onMessage", "message", "sender", "sendResponse", "type", "enabled", "_message$payload", "payload", "jwt", "search", "fetch", "method", "headers", "body", "JSON", "stringify", "pageSize", "requestDataObject", "requestDataType", "rootCode", "then", "response", "ok", "Error", "concat", "status", "json", "data", "Array", "isArray", "userOptions", "map", "user", "_objectSpread", "value", "name", "loginName", "label", "error"], "sourceRoot": ""}