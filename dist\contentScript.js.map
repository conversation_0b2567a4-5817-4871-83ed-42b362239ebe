{"version": 3, "file": "contentScript.js", "mappings": "AACAA,QAAQC,IAAI,yBAGZC,OAAOC,QAAQC,MAAMC,IAAI,SAAU,SAACC,GACA,SAAlBA,EAAOC,OAErBP,QAAQC,IAAI,qCAEZD,QAAQC,IAAI,qCAEhB,GAGAO,OAAOC,iBAAiB,UAAW,SAACC,GAE9BA,EAAMC,SAAWH,QAEjBE,EAAME,KAAKC,MAA4B,cAApBH,EAAME,KAAKC,MAChCb,QAAQC,IAAI,8BAA+BS,EAAME,KAErD", "sources": ["webpack://faw-mock/./src/contentScript.js"], "sourcesContent": ["// 内容脚本\nconsole.log('Content script loaded');\n\n// 检查是否启用\nchrome.storage.local.get('enable', (result) => {\n  const enabled = result.enable === 'true';\n  if (enabled) {\n    console.log('Extension is enabled on this page');\n  } else {\n    console.log('Extension is disabled on this page');\n  }\n});\n\n// 监听来自页面的消息\nwindow.addEventListener('message', (event) => {\n  // 确保消息来自同一个窗口\n  if (event.source !== window) return;\n  \n  if (event.data.type && event.data.type === 'FROM_PAGE') {\n    console.log('Received message from page:', event.data);\n  }\n});"], "names": ["console", "log", "chrome", "storage", "local", "get", "result", "enable", "window", "addEventListener", "event", "source", "data", "type"], "sourceRoot": ""}