import{formatDate,LoadingSpinner,TimeIntervalUpdater}from"../utils/utils.js";import{STATUS_MAP,getCode,DOING,PREPARE,DONE,ERROR,ABORT,JUMP}from"../utils/faw.js";import{$post}from"../utils/fetch.js";const result=document.querySelector("#result"),deployBtn=document.querySelector("#deployBtn"),abortBtn=document.querySelector("#abortBtn"),codeInput=document.querySelector("#code");let checkTimer=null,loadingTimer=null,calculateTimer=null;deployBtn.addEventListener("click",function(){const e=codeInput.value.trim();if(!e)return alert("请输入用例号");deployBtn.setAttribute("data-code",e),startPipeline(e)}),abortBtn.addEventListener("click",function(){const e=abortBtn.getAttribute("data-buildId"),t=deployBtn.getAttribute("data-code");e&&abortPipeline(e,t)});const startCalculateProcess=(e,t)=>{abortBtn.style.display="block",abortBtn.setAttribute("data-buildId",e),loadingTimer||(loadingTimer=new LoadingSpinner("#loadingStr"),loadingTimer.start()),!calculateTimer&&t&&(calculateTimer=new TimeIntervalUpdater("#timeIntervalStr"),calculateTimer.start(t)),checkTimer=window.setTimeout(()=>checkProgress(e),2e4)},resetStatus=()=>{clearTimeout(checkTimer),checkTimer=null,loadingTimer.end(),calculateTimer.end(),loadingTimer=null,calculateTimer=null,abortBtn.style.display="none"},getSuffixStr=e=>{switch(e){case DOING:return'<span id="loadingStr"></span>';case DONE:return"😊";case ERROR:return"😟";case ABORT:return"😶";case JUMP:return"✈";default:return""}},renderDeployStatus=e=>{const t=e.endTime?formatDate(e.endTime):'<span id="timeIntervalStr">-</span>';result.innerHTML=`\n      <p>流水线id【${e.buildId}】： ${STATUS_MAP[e.status]} | ${e.creator} | ${t}</p><br/>\n      <ul>\n        ${e.stageList.map((e,t,a)=>(e.subStages?.length?e.subStages:[e]).map(e=>`\n                <li>\n                  [${e.stageName}] ${STATUS_MAP[e.status]} ${getSuffixStr(e.status)}</i><br/>\n                  开始时间：${formatDate(e.startTime)} <br/>\n                  结束时间：${formatDate(e.endTime)} <br/>\n                  ${t<a.length-1?"----------------------------------------------------":""}\n                </li>\n              `)).flat().join("")}\n      </ul>\n    `},checkProgress=async e=>{if(!e)return;const{success:t,data:a}=await $post("/local-faw/api/buildService/api/app/build/detail",{buildId:e});t&&(renderDeployStatus(a),[PREPARE,DOING].includes(a.status)?startCalculateProcess(e,a.stageList?.[0]?.startTime):resetStatus())},startPipeline=async e=>{const{success:t,data:a,message:r,code:i}=await $post("/local-faw/api/buildService/api/app/build/startPipeline",{applicationCode:getCode(e),environmentType:"daily"});if(t)result.innerHTML=`开始构建，构建ID：${a.buildId}`,checkProgress(a.buildId);else if("409"===i){const e=r.match(/(?<=id:\s)\d+/)?.[0];result.innerHTML=`构建失败，当前正在构建的ID：${e}`,checkProgress(e)}},abortPipeline=async(e,t)=>{const{success:a,data:r}=await $post("/local-faw/api/buildService/api/app/build/abortPipeline",{buildId:e,applicationCode:getCode(t),environmentType:"daily"});a&&(result.innerHTML=`<p>流水线【${e}】已终止</p>`)};