{"name": "Mock Response Modifier", "version": "1.0", "description": "An Edge extension to modify browser request responses.", "manifest_version": 3, "action": {"default_popup": "popup.html"}, "icons": {"48": "icons/logo.png", "128": "icons/logo.png"}, "permissions": ["storage", "tabs", "scripting", "clipboardWrite", "declarativeNetRequest", "declarativeNetRequestWithHostAccess"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["contentScript.js"]}]}