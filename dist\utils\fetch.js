export const API_PREFIX="http://localhost";export const $fetch=async(t,e={})=>{const o=new URLSearchParams(window.location.search).get("token");if(!o)return;const n=new AbortController,r=setTimeout(()=>n.abort(),6e4);try{const r=await fetch(t,{...e,headers:{Authorization:`Bearer ${o}`,...e.headers},signal:n.signal}),{status:s}=r;if(200===s)return r.json();throw new Error(`${s}`)}catch(t){return t}finally{clearTimeout(r)}};export const $get=(t,e={})=>{const o=Object.entries(e).map(([t,e])=>`${t}=${e}`),n=o.length?`?${o.join("&")}`:"";return $fetch(`${API_PREFIX}${t}${n}`)};export const $post=(t,e={})=>$fetch(`${API_PREFIX}${t}`,{method:"POST",body:JSON.stringify(e),headers:{"Content-Type":"application/json"}});