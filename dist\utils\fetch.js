export const $fetch=async(t,e={})=>{const n=new URLSearchParams(window.location.search).get("token");if(!n)return;const o=new AbortController,r=setTimeout(()=>o.abort(),6e4);try{const r=await fetch(t,{...e,headers:{Authorization:`${n}`,...e.headers},signal:o.signal}),{status:s}=r;if(200===s)return r.json();throw new Error(`${s}`)}catch(t){return t}finally{clearTimeout(r)}};export const $get=(t,e={})=>{const n=Object.entries(e).map(([t,e])=>`${t}=${e}`),o=n.length?`?${n.join("&")}`:"";return $fetch(`${t}${o}`)};export const $post=(t,e={})=>$fetch(`${t}`,{method:"POST",body:JSON.stringify(e),headers:{"Content-Type":"application/json"}});