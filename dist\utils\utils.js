export const checkUrl=async(t,e)=>!!t&&new Promise((r,i)=>{e&&r(t instanceof RegExp?t.test(e):t.includes(e)),chrome.tabs.query({active:!0,currentWindow:!0},e=>{chrome.runtime.lastError&&i(chrome.runtime.lastError);const n=e[0]?.url;r(t instanceof RegExp?t.test(n):t.includes(n))})});export const getToken=async t=>new Promise((e,r)=>{chrome.tabs.query({active:!0,currentWindow:!0},i=>{chrome.scripting.executeScript({target:{tabId:i[0].id},function:t=>localStorage.getItem(t),args:[t]},t=>{if(t?.[0]?.result){const i=JSON.parse(t[0].result)?.value;i?e(i):r("JWT not valid.")}else r("JWT not found in localStorage.")})})});export const loadStatic=async t=>{const e=await fetch(chrome.runtime.getURL(t));return await e.json()};export const formatDate=t=>{if(!t)return"-";const e=new Date(t);return`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")} ${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}:${e.getSeconds().toString().padStart(2,"0")}`};export class LoadingSpinner{constructor(t,e=200){this.selector=t,this.interval=e,this.loadingStrMap=["-","\\","|","/"],this.loadingStrLen=this.loadingStrMap.length,this.loadingStrIndex=0,this.timer=null}start(){this.selector&&(this.timer||(this.timer=window.setInterval(()=>{const t=document.querySelector(this.selector);t&&(t.innerHTML=this.loadingStrMap[this.loadingStrIndex],this.loadingStrIndex===this.loadingStrLen-1?this.loadingStrIndex=0:this.loadingStrIndex+=1)},this.interval)))}end(){this.timer&&(clearInterval(this.timer),this.timer=null,this.loadingStrIndex=0)}}export class TimeIntervalUpdater{constructor(t,e=1e3){this.selector=t,this.interval=e,this.timer=null}calculateTime(t,e){const r=new Date(t),i=e?new Date(e):new Date,n=Math.floor((i-r)/1e3);return{hours:Math.floor(n/3600),minutes:Math.floor(n%3600/60),seconds:n%60}}start(t=null){this.selector&&(this.timer||(t&&(this.startTime=t),this.timer=window.setInterval(()=>{const{hours:t,minutes:e,seconds:r}=this.calculateTime(this.startTime),i=document.querySelector(this.selector);i&&(i.innerText=`已进行 ${t} 时 ${e} 分 ${r} 秒`)},this.interval)))}end(){this.timer&&(clearInterval(this.timer),this.timer=null)}}