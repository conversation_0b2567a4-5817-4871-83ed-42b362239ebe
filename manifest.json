{"manifest_version": 3, "name": "Mock Response Modifier", "version": "1.0", "description": "An Edge extension to modify browser request responses.", "permissions": ["declarativeNetRequestFeedback", "declarativeNetRequest", "declarativeNetRequestWithHostAccess", "storage", "tabs", "scripting"], "host_permissions": ["*://*/*"], "icons": {"48": "icons/logo.png", "128": "icons/logo.png"}, "action": {"default_popup": "popup.html"}, "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["public/*"]}], "background": {"service_worker": "background.js", "type": "module"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content-script.js"]}]}