{"name": "faw-mock", "version": "1.0.0", "description": "Chrome extension with React and Ant Design", "main": "index.js", "scripts": {"start": "webpack --watch --mode=development", "dev": "webpack serve --mode=development", "build": "webpack --mode=production"}, "dependencies": {"@ant-design/icons": "^4.7.0", "antd": "^4.24.0", "crypto-js": "^4.2.0", "react": "^17.0.2", "react-dom": "^17.0.2"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/preset-env": "^7.16.0", "@babel/preset-react": "^7.16.0", "babel-loader": "^8.2.3", "copy-webpack-plugin": "^9.0.1", "css-loader": "^6.5.1", "html-webpack-plugin": "^5.5.0", "style-loader": "^3.3.1", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}}