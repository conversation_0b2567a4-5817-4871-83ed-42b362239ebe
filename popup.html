<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Mock Response Modifier</title>
    <style>
      * {
        margin: 0;
        padding: 0;
      }

      .popup-page {
        min-width: 100px;
        display: flex;
        flex-direction: column;
        /* align-items: center; */
        justify-content: center;
        padding: 10px;
      }

      .md-switch {
        display: flex;
        align-items: center;
        font-size: 14px;
      }

      .md-switch-thumb {
        flex-shrink: 0;
        width: 36px;
        height: 18px;
        border-radius: 18px;
        position: relative;
        background-color: #ddd;
        margin-right: 8px;
        cursor: pointer;
      }

      .md-ripple {
        width: 14px;
        height: 14px;
        position: absolute;
        top: 0;
        left: 2px;
        bottom: 0;
        margin: auto;
        border-radius: 50%;
        transition: all 0.5s;
        background: #eee;
      }

      .md-switch-thumb.checked {
        background: #eee;
      }

      .md-switch-thumb.checked .md-ripple {
        transform: translateX(16px);
        background: #0078d4;
      }

      .md-switch-thumb.checked .md-switch-label {
        color: #0078d4;
      }

      .md-switch-label {
        flex-shrink: 0;
      }

      button {
        display: block;
        margin-top: 5px;
        border-color: transparent;
        padding: 2px 6px;
        border-radius: 3px;
        cursor: pointer;
      }

      button:hover {
        opacity: 0.8;
      }

      #copyJwtBtn {
        background-color: #0078d4;
        color: #fff;
      }

      #copySitBtn {
        background-color: #149530;
        color: #fff;
      }

      #deployBtn {
        background-color: #fff;
        color: #0078d4;
        border-color: #0078d4;
      }

      /* 人员切换样式 */
      .user-switch {
        display: flex;
        align-items: center;
      }

      .user-select {
        flex: 1;
        height: 24px;
        border-radius: 3px;
        padding: 0 4px;
        margin-right: 5px;
      }

      #switchUserBtn {
        background-color: #0078d4;
        color: #fff;
        margin-top: 0;
      }
    </style>
  </head>
  <body>
    <div class="popup-page">
      <div class="md-switch">
        <div class="md-switch-thumb" id="switchBtn">
          <div class="md-ripple"></div>
        </div>
        <!-- 开启【生产环境】切账号权限 & 增加菜单用例 -->
        <p class="md-switch-label">启用规则</p>
        <input
          id="switchValue"
          placeholder=""
          hidden
          type="checkbox"
          value="false"
        />
      </div>
      <!-- 人员切换功能 -->
      <div class="user-switch">
        <select id="userSelect" class="user-select">
          <option value="">-- 选择人员 --</option>
        </select>
      </div>
      <button id="switchUserBtn">切换人员</button>
      <!-- 适用于【任意环境】复制jwt token到剪贴板（包含Bearer） -->
      <button id="copyJwtBtn">复制TOKEN</button>
      <!-- 适用于在【本地环境】复制duerfu sit用token登录的url到剪贴板 -->
      <button id="copySitBtn">复制SIT URL</button>
      <!-- 模拟iwork应用部署（只能部署sit环境前端） -->
      <button id="deployBtn">去部署</button>
    </div>
    <script type="module" src="./popup/ruleSwitch.js"></script>
    <script type="module" src="./popup/copyBtn.js"></script>
    <script type="module" src="./popup/deployBtn.js"></script>
    <script type="module" src="./popup/userSwitch.js"></script>
  </body>
</html>
