import { getToken } from "../utils/utils.js";
import { TOKEN_KEY } from "../utils/faw.js";

const copyJwtBtn = document.querySelector("#copyJwtBtn");
const copySitBtn = document.querySelector("#copySitBtn");

const getCallback = (
  target,
  successTip = "复制成功 √",
  errorTip = "复制失败 ×"
) => {
  const originTip = target.innerHTML;
  const final = () => {
    window.setTimeout(() => {
      target.innerHTML = originTip;
    }, 1000);
  };
  return {
    errorCb: () => {
      target.innerHTML = errorTip;
      final();
    },
    successCb: () => {
      target.innerHTML = successTip;
      final();
    },
  };
};

copyJwtBtn.addEventListener("click", async function () {
  try {
    const jwt = await getToken(TOKEN_KEY);
    copyToClipboard(`Bearer ${jwt}`, copyJwtBtn);
  } catch (error) {
    console.log("error", error);
  }
});

copySitBtn.addEventListener("click", async function () {
  try {
    const jwt = await getToken(TOKEN_KEY);
    // 在本地环境下复制token 然后复制url在无痕模式打开 然后切到IT工程师角色
    // 应用-授权管理-菜单管理（新增菜单只能在生产环境 但是菜单的绑定和更新可以在sit环境
    copyToClipboard(
      `https://sit-iwork.faw.cn?token=Bearer ${jwt}&userId=1471301459636428802&source=zerotrust&tenantId=YQJT&systemId=BA-0222&loginName=U2FsdGVkX19Zdsxql/rGIqB/zwd2fyNU780vX+nZEyM=&shortName=fcw&workbenchGroupCode=WB007&roleGroupCode=fcw&checkIp=&cnName=%25E6%259D%259C%25E5%25B0%2594%25E4%25BB%2598&idmId=u2019272664&encrypt=U2FsdGVkX18qal+wv0x1Kg1uS5CEwYs7Ea/5a7FcquxIEWWZNhiHJSZis+TXUhjCXeD+zd73BQTHyjocepuX/yFFsLOSyZ5kEj7ArpBntgRS9/AEQqsRWlE+xkTXA8wMpvaVF3Ad7NwLhRbVc1imd4wfDGSjsBLesv+6LS533yU=`,
      copySitBtn
    );
  } catch (error) {
    console.log("error", error);
  }
});

async function copyToClipboard(text, target) {
  const { errorCb, successCb } = getCallback(target);
  try {
    await navigator.clipboard.writeText(text);
    successCb();
    console.log("复制成功----", text);
  } catch (error) {
    errorCb();
  }
}
