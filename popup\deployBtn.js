import { getToken, checkUrl } from "../utils/utils.js";
import { FAW_REG, TOKEN_KEY } from "../utils/faw.js";

const deployBtn = document.querySelector("#deployBtn");

deployBtn.addEventListener("click", async function () {
  const urlValid = await checkUrl(FAW_REG);
  if (!urlValid) {
    return;
  }
  try {
    const token = await getToken(TOKEN_KEY);
    chrome.tabs.create({
      url: `deploy/index.html?token=${token}`,
    });
  } catch (error) {
    console.log("error", error);
  }
});
