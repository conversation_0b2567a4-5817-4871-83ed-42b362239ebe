import { checkUrl } from "../utils/utils.js";
import { FAW_HOME_REG } from "../utils/faw.js";

const switchBtn = document.querySelector("#switchBtn");
const switchVal = document.querySelector("#switchValue");

let loading = false;
let urlValid = false;

switchBtn.addEventListener("click", async function () {
  if (loading || !urlValid) {
    return;
  }
  loading = true;
  const value = switchVal.value === "true" ? "false" : "true";
  await chrome.storage.local.set({ enable: value });
  loading = false;
  switchVal.value = value;
  switchBtn.classList.toggle("checked");
  chrome.tabs.reload();
});

const init = async () => {
  try {
    const { enable } = await chrome.storage.local.get("enable");
    switchVal.value = enable;
    urlValid = await checkUrl(FAW_HOME_REG);
    if (enable === "true" && urlValid) {
      switchBtn.classList.add("checked");
    }
  } catch (error) {
    console.log("init error", error);
  }
};
init();
