// 人员数据，可以根据需要扩展
const userList = [
    {
        name: "程博",
        userId: "cb",
        value: '{"id":"U2FsdGVkX199alZK1gqcUb2PzktNEwrBidbEVcCoEXnvM+SYQrGcFwyqHfcrrQ0z","systemId":"U2FsdGVkX19TovYCX5efc+VsY0Vf5864p+S2/JUpi/E=","tenantId":"U2FsdGVkX1/gjyBJ8uUaR5zwuoK3SGNg7DWPZCK6DmM=","hrId":"U2FsdGVkX1/sQ56VGAsNLMa3UvRsRRDkwzDa+GK/PvA=","idmId":"U2FsdGVkX1+3xqJT/xZVbjUAr+UKJ0DXazkQbfpIIL4=","source":"U2FsdGVkX1+wF5exCOOoerpQGa5ns72dQ+30PcuaiWg=","workbenchGroupCode":"U2FsdGVkX19F3+Jz4Fe+/pvqDhV7iSbwENdioDZPVBM=","name":"U2FsdGVkX1+S8xPNIdULCHix0UAE5ZNWbpJlR1Ymg8I=","loginName":"U2FsdGVkX19CFh7vaJQRNjnqDCL+Km5F19x3vC4rz6o=","grayInfo":"U2FsdGVkX1/212PHoUuAuKWGFl0JcMmByo7qlnNqAI2D+Oi1F+Q0QSQcVFYrY42Z6yUuHvcWzPVvg1bcTdS2B+f1BwdQHIP4rx1Doei5LRs=","dingTalkId":"U2FsdGVkX1+8A/mFJB1Ba0KkSR2m9s0O63i/5Vk9xvU=","initId":"U2FsdGVkX1/+5Tn4J81vjL1dTViUBFDwjdXA3sByJBplIrMeCF0dJQM0cmK8LVWS","initIdmid":"U2FsdGVkX19P3HoyFxcTg1PpPGC3RL0qdvRDZCATJOQ=","initUserName":"U2FsdGVkX1+vYw6Rk+WrNPt0WYqT6qCqoELbFRO1UDg=","initLoginName":"U2FsdGVkX18xifFpa0uLnzPBUF50M19keJ/NI9D417Kahi6Nx0ComhM8Hs4G1g/b"}'
    },
    {
        name: "段思莹",
        userId: "dsy",
        value: '{"id":"U2FsdGVkX19HK6nm0g43Ar3z487814Wz0n/NlPDckm2YlCaWb+ANRoplH+dSnaWd","systemId":"U2FsdGVkX1+cZJcd2oBw0ucYtyV4xkEhQsa+9E3BRJo=","tenantId":"U2FsdGVkX1+npfSLuX8S1MVHZDjIgIBAb3NPsKKK/8I=","hrId":"U2FsdGVkX1+j1Vm/KxO9t0SW8BjkSpUav9Dnk+jaPag=","idmId":"U2FsdGVkX19HRSWhuEigaikTNL6B1ReJU7MySYbigJc=","source":"U2FsdGVkX19qBnn376ZzjDS6yekV9tkX0+cx2Brp2D4=","workbenchGroupCode":"U2FsdGVkX1+wYT2hf9bH1CoKupUTgxY1toEJLWSvwZY=","name":"U2FsdGVkX1+AuNLejSpg2DZiUYJORYbNDVM+I7U1jF8=","loginName":"U2FsdGVkX18sDuWtntbKAzrQB7mS3RMBPLWqCpEQq8a9/tUvnaJGcIoiWx9ADfn9FZT7uPXk8MiOjJnLThBKVw==","grayInfo":"U2FsdGVkX1+bmItQVovEjwLx25ArITA3XFQjy90BRe7qP1uYkEwx40+QXnJiX5oBZKbBdSwZ5R4qCAvxvuYFNWsf963D/RUKM44xQDv9H3c=","dingTalkId":"U2FsdGVkX18i3s8YnYz+91+SlSzVVPOGFxplTkshcLI=","initId":"U2FsdGVkX19T4kmemLv2AGGAHLBsJMZVjVmzG6kOwh4Jmz5kpwQD+RIg2GLfvdsv","initIdmid":"U2FsdGVkX1+uGT2yRk3pUGTlky/Wro3Qw4ADxbGG+eo=","initUserName":"U2FsdGVkX18dfZoHzUbzlIioxIKUW+Fg8RnkR93Kl5s=","initLoginName":"U2FsdGVkX18EzhRW3RGkgoGK+LCMhtVceSFmsdCl0Dc=","isExternalSettings":"U2FsdGVkX183I/MAbdMX3rMSjkY6NziH0M3N61xQnqQ="}'
    },
    {
        name: "刘春生",
        userId: "lcs",
        value: '{"id":"U2FsdGVkX18o+DF3BGUjrC7xsoB6vZNYNvSQz+F92+AGRHyMPClgG1SVPe1CZGyU","systemId":"U2FsdGVkX19jTGu+LqLp04UeHpp0el6EiOnBmkSAFFw=","tenantId":"U2FsdGVkX1+QGgl/dQH+5yn3Dvcx1IB7NBDaDx6HdE4=","idmId":"U2FsdGVkX1/ppdz+I+bxdpB0ZwgEdheZ4f/FTK6SDCc=","source":"U2FsdGVkX18RVGTuPJ001EsPMjkgW4Yst6lY73W2Kvg=","workbenchGroupCode":"U2FsdGVkX1/wL8ZhCQcNKoP3bQmqenFRtxIvsXZWgRA=","name":"U2FsdGVkX18/GXajV/Ezt2bkAwCOqzFRzPe15xMDL+U=","loginName":"U2FsdGVkX1861frZqNjFl76VUbt0cZ1bVKUhbvLPpJDNJKjWOigH38+T6Iv7kWF4","grayInfo":"U2FsdGVkX19NxI36frRepZRjJINz+nCYwp9vfwrnYP53a1wClNJ71g32lTcB0H3DdODVqP1YhPW2FrJ7cZctwrvoanwdBbuNMRNVtyzkSVM=","dingTalkId":"U2FsdGVkX19TNX9sUMhmBL3KWE76P4xJ85f+XHY4XbA=","initId":"U2FsdGVkX1/7snpWmHUdh7ZajJuaSAr77+kxvEo0ZiBKJU7MHNE20h9KxlmQG6Gm","initIdmid":"U2FsdGVkX18LxzvZucEV7LiGWkO8Ere1XEHktel68VM=","initUserName":"U2FsdGVkX18e+iwFp31Yh4FByj2UT2qWIMuJsYRMsq0=","initLoginName":"U2FsdGVkX1+l9vFAMVglKzX4zM2E3x7O9tE3ZURfSQc="}'
    },
    {
        name: "吴新欢",
        userId: "wxh",
        value: '{"id":"U2FsdGVkX18u+RekBPK8aBwErxTa1VjurUhaF8I0vJ5yuCntXaXSP7CVgfPkMH4R","systemId":"U2FsdGVkX1/t+hNuXxmkTKym2num/bwxdAz0OqvnNdE=","tenantId":"U2FsdGVkX189J39gfHC1a5ryC0Gq2bgXyDVki/rXuxg=","idmId":"U2FsdGVkX180eNk/9RBV8CL2CrbU/YGp6FxqpvIm198=","source":"U2FsdGVkX1+I+sUCvCv1ulTXNcw9YYgual4v/DdVH4Q=","workbenchGroupCode":"U2FsdGVkX1+4l/7DEKbUOacsiWYyMuJmT6tuseO0yB8=","name":"U2FsdGVkX19POx5/Fv6ssfjeFiW12mDA7qEBtPHxg/E=","loginName":"U2FsdGVkX19PDOnkTtkikMgB3VmdzIDbkjTX7xoa+PUlZj1JL75WirMBScxr43Wu","grayInfo":"U2FsdGVkX1+eqOjxGwB3HXKMl7MyDaR7XKOxhVgr8/WjDjBS6oTfD960AKq4N+iSRyVevG6uxBC/3eDfUBjzFtc6Ke3gm7+Rf9VFe3jf+Y0=","dingTalkId":"U2FsdGVkX1+8pBkahek7RlDU7trOh73hRYpFc2xXXac=","initId":"U2FsdGVkX18OBI3SVzYJq/sResneGkEZdlMJQF5I7OWW0ALCz/6kjgc13d3asTsY","initIdmid":"U2FsdGVkX18nE1egX/6I24RNmcJ0KIPkII9iSYomdFw=","initUserName":"U2FsdGVkX18cA2K8xlyHDPQ7ZO/0SDcNrdnWeLqa5E0=","initLoginName":"U2FsdGVkX18/30P0vhhI1BbHwDt7irUkzCTH+ogU2zM="}'
    },
    {
        name: "王洪伟",
        userId: "whw",
        value: '{"id":"U2FsdGVkX19DZnn7nuLoYCmDWGKiqlLE36ycUPHIpqJJTDVQkBFq8stQ1GIrndgr","systemId":"U2FsdGVkX1+Co1gfN6O3eAJ5cCM0ekUmTwgEKv//LT4=","tenantId":"U2FsdGVkX1/YXeWSrK/fZ9FFTJN82MU4dMKaGwc00Qc=","hrId":"U2FsdGVkX19gLpi8doiEgMiql6mR65SRR4wo5uWjWhg=","idmId":"U2FsdGVkX19anAtoAeWYZgjr9VBg7ewMvwrgecIlyCA=","source":"U2FsdGVkX18CjMb8x0KTd6LKDNpf6HPgcdk3DtrG0yg=","workbenchGroupCode":"U2FsdGVkX18d0jV2SU2GIjTWEZas5+ecp/hexOZ1y6Q=","name":"U2FsdGVkX1+7atkQI4Xo7pwG03GaTwYhJEceZSIoptI=","loginName":"U2FsdGVkX1/WPNYJDRFW4All8xU/pdpgLoQdYXpetTw=","grayInfo":"U2FsdGVkX19aJn3BexyeG6WdX0kC+9XzOkxtY7m9QtS/i3uS4jVsDHIB6cTn6u6uz/gucUGqvpERQiPAHADlHvQNjfVLPMuX/jsK4Rd75PM=","dingTalkId":"U2FsdGVkX1/qdwWxg57Kgf979G3kM+n8dKP+k5XxLGw=","initId":"U2FsdGVkX1+sNKrsCbo+MU6zBjCzVZuSOys2CPya4c3sDniALQROssxzZNr1mny1","initIdmid":"U2FsdGVkX18EYRW6rP4lrpsF6xGljWGvvBXxhSvcpo4=","initUserName":"U2FsdGVkX19pBQHFwOTvkWQxzxvT3McpvjBsGmQL80U=","initLoginName":"U2FsdGVkX1/y9QaO8b2lOqWUNdHjSrQVoSSfMHii87g=","isExternalSettings":"U2FsdGVkX19/QwtKh3AF7OJsKmE9IONweEtdw/F0HwI="}'
    }
];

const userSelect = document.querySelector("#userSelect");
const switchUserBtn = document.querySelector("#switchUserBtn");

// 初始化下拉列表
const initUserSelect = () => {
    userList.forEach(user => {
        const option = document.createElement("option");
        option.value = user.userId;
        option.textContent = user.name;
        userSelect.appendChild(option);
    });
};

// 切换用户
const switchUser = async () => {
    const userId = userSelect.value;
    if (!userId) {
        alert("请选择人员");
        return;
    }

    const selectedUser = userList.find(user => user.userId === userId);
    if (!selectedUser) return;

    try {
        // 将用户信息存储到 localStorage
        await chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.scripting.executeScript({
                target: { tabId: tabs[0].id },
                function: (userInfo) => {
                    localStorage.setItem("de0505_userInfo", userInfo.value);
                    return true;
                },
                args: [selectedUser]
            }, (results) => {
                if (results && results[0]?.result) {
                    alert(`已切换到 ${selectedUser.name}`);
                } else {
                    localStorage.removeItem("de0505_userInfo");
                    alert("切换失败");
                }
            });
        });
    } catch (error) {
        console.error("切换用户失败", error);
        alert("切换失败: " + error.message);
    }
};

// 初始化
const init = async () => {
    initUserSelect();

    // 获取当前用户信息
    try {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (!tabs || !tabs[0] || !tabs[0].id) {
                console.error('无法获取当前标签页');
                return;
            }
            
            console.log('当前标签页:', tabs[0].url);
            
            chrome.scripting.executeScript({
                target: { tabId: tabs[0].id },
                function: () => {
                    console.log('在页面中执行脚本');
                    const value = localStorage.getItem("de0505_userInfo");
                    console.log('页面中的用户信息:', value);
                    return value;
                }
            }, (results) => {
                if (chrome.runtime.lastError) {
                    console.error('执行脚本出错:', chrome.runtime.lastError.message);
                } else {
                    const userInfo = results[0]?.result;
                    console.log('页面中的用户信息:', userInfo);
                    
                    // 如果获取到了用户信息，则设置下拉框的值
                    if (userInfo) {
                        try {
                            // 尝试解析 JSON 字符串
                            const parsedUserInfo = JSON.parse(userInfo);
                            // 查找匹配的用户
                            const matchedUser = userList.find(user => 
                                user.value === userInfo || // 直接匹配 value 字符串
                                user.userId === parsedUserInfo.userId || // 尝试匹配 userId
                                user.userId === parsedUserInfo.id // 或者尝试匹配 id
                            );
                            
                            if (matchedUser) {
                                userSelect.value = matchedUser.userId;
                                console.log('已设置当前用户:', matchedUser.name);
                            }
                        } catch (e) {
                            // 如果解析失败，尝试直接匹配
                            const matchedUser = userList.find(user => user.value === userInfo);
                            if (matchedUser) {
                                userSelect.value = matchedUser.userId;
                                console.log('已设置当前用户:', matchedUser.name);
                            }
                        }
                    }
                }
            });
        });
    } catch (error) {
        console.log("获取当前用户信息失败", error);
    }
};

// 事件监听
switchUserBtn.addEventListener("click", switchUser);

// 初始化
init();



