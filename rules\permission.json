[{"id": 1, "priority": 1, "action": {"type": "redirect", "redirect": {"extensionPath": "/public/permission.json"}}, "condition": {"urlFilter": "https://fcw.faw.cn/api-dev/qfc-business-bcc/user/home/<USER>", "resourceTypes": ["xmlhttprequest"]}}, {"id": 2, "priority": 1, "action": {"type": "redirect", "redirect": {"extensionPath": "/public/menu.json"}}, "condition": {"urlFilter": "https://iwork.faw.cn/api-dev/qfc-base-auth/menu/wbMenuAll", "resourceTypes": ["xmlhttprequest"]}}]