// 后台脚本
console.log('Background script loaded');

// 初始化扩展
chrome.runtime.onInstalled.addListener(() => {
  console.log('Extension installed');
  
  // 设置默认值
  chrome.storage.local.get('enable', (result) => {
    if (result.enable === undefined) {
      chrome.storage.local.set({ enable: 'false' });
    }
  });
});

// 监听消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'getStatus') {
    chrome.storage.local.get('enable', (result) => {
      sendResponse({ enabled: result.enable === 'true' });
    });
    return true; // 异步响应
  }
  
  // 处理获取用户列表的请求
  if (message.type === 'FETCH_USER_LIST') {
    const { jwt, search } = message.payload;
    
    fetch('http://***********:410/api-dev/qfc-business-bcc/v2/user/selector/getUserWithRecent', {
      method: 'POST',
      headers: {
        'Authorization': jwt,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        search: search,
        pageSize: 6,
        requestDataObject: {},
        requestDataType: "NORMAL",
        rootCode: "CUSTOM_TASK"
      })
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      if (data && Array.isArray(data.data)) {
        // 转换API返回的数据格式
        const userOptions = data.data.map(user => ({
          ...user,
          value: user.name + `(${user.loginName})`,
          label: user.name + `(${user.loginName})`
        }));
        sendResponse({ data: userOptions });
      } else {
        sendResponse({ error: '获取用户列表失败，数据格式不正确' });
      }
    })
    .catch(error => {
      console.error('获取用户列表失败:', error);
      sendResponse({ error: '获取用户列表失败: ' + error.message });
    });
    
    return true; // 异步响应
  }
});
