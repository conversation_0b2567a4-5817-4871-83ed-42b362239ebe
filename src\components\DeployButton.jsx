import React, { useState, useEffect } from 'react';
import { Button, Tooltip } from 'antd';
import { DeploymentUnitOutlined } from '@ant-design/icons';
import { deploy } from '../services/deployService';
import { isExtensionEnvironment } from '../utils/chromeApi';

const DeployButton = () => {
    const [hasFawTab, setHasFawTab] = useState(false);

    // 检查是否有FAW标签页打开
    const checkFawTabs = async () => {
        if (!isExtensionEnvironment()) {
            // 开发环境中总是返回true
            setHasFawTab(true);
            return;
        }

        return new Promise((resolve) => {
            chrome.tabs.query({ url: 'https://iwork.faw.cn/*' }, (tabs) => {
                if (chrome.runtime.lastError) {
                    console.error('查询FAW标签页失败:', chrome.runtime.lastError);
                    setHasFawTab(false);
                    resolve(false);
                    return;
                }

                const hasTab = tabs && tabs.length > 0;
                setHasFawTab(hasTab);
                resolve(hasTab);
            });
        });
    };

    useEffect(() => {
        // 检查是否有FAW标签页
        const checkTabs = async () => {
            await checkFawTabs();
        };
        checkTabs();
        // 定期检查FAW标签页状态（每5秒检查一次）
        const interval = setInterval(checkTabs, 5000);
        return () => clearInterval(interval);
    }, []);

    const handleDeploy = () => {
        if (!hasFawTab) {
            return;
        }
        deploy();
    };

    // 计算按钮是否可用
    const isButtonEnabled = hasFawTab;

    // 生成提示信息
    const getTooltipConfig = () => {
        if (!hasFawTab) {
            return {
                title: (
                    <div style={{ textAlign: 'center' }}>
                        <div style={{ marginBottom: '4px', fontWeight: 'bold' }}>
                            🚫 无法使用部署功能
                        </div>
                        <div style={{ fontSize: '12px', opacity: 0.9 }}>
                            请先在新标签页中打开
                        </div>
                        <div style={{
                            fontSize: '12px',
                            color: '#1890ff',
                            fontFamily: 'monospace',
                            marginTop: '2px'
                        }}>
                            https://iwork.faw.cn
                        </div>
                    </div>
                ),
                color: '#ff4d4f',
                placement: 'top'
            };
        }
        return {
            title: (
                <div style={{ textAlign: 'center' }}>
                    <div style={{ fontWeight: 'bold' }}>
                        ✅ 点击开始部署
                    </div>
                    <div style={{ fontSize: '12px', opacity: 0.9, marginTop: '2px' }}>
                        将从 FAW 页面获取 Token
                    </div>
                </div>
            ),
            color: '#52c41a',
            placement: 'top'
        };
    };

    const tooltipConfig = getTooltipConfig();

    return (
        <Tooltip
            title={tooltipConfig.title}
            color={tooltipConfig.color}
            placement={tooltipConfig.placement}
            mouseEnterDelay={0.3}
            mouseLeaveDelay={0.1}
            overlayStyle={{ maxWidth: '280px' }}
        >
            <Button
                icon={<DeploymentUnitOutlined />}
                onClick={handleDeploy}
                disabled={!isButtonEnabled}
                style={{
                    marginTop: 8,
                    borderColor: isButtonEnabled ? '#0078d4' : '#d9d9d9',
                    color: isButtonEnabled ? '#0078d4' : '#00000040'
                }}
                block
            >
                去部署
            </Button>
        </Tooltip>
    );
};

export default DeployButton;