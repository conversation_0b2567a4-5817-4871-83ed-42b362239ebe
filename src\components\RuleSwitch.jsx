import React, { useState, useEffect } from 'react';
import { Switch } from 'antd';

const RuleSwitch = () => {
  const [enabled, setEnabled] = useState(false);

  // 初始化开关状态
  useEffect(() => {
    chrome.storage.local.get("enable", ({ enable }) => {
      setEnabled(enable === "true");
    });
  }, []);

  // 切换开关状态
  const handleSwitchChange = (checked) => {
    setEnabled(checked);
    chrome.storage.local.set({ enable: checked.toString() });
  };

  return (
    <div className="switch-container">
      <Switch checked={enabled} onChange={handleSwitchChange} />
      <span className="switch-label">启用规则</span>
    </div>
  );
};

export default RuleSwitch;