import React from 'react';
import { Button } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import { copyToken, copySitUrl } from '../services/tokenService';

const TokenButtons = () => {
  return (
    <div className="section">
      <Button
        type="primary"
        icon={<CopyOutlined />}
        onClick={copyToken}
        block
      >
        复制TOKEN
      </Button>

      <Button
        type="primary"
        icon={<CopyOutlined />}
        onClick={copySitUrl}
        style={{ backgroundColor: '#149530', borderColor: '#149530', marginTop: 8 }}
        block
      >
        复制SIT URL
      </Button>
    </div>
  );
};

export default TokenButtons;