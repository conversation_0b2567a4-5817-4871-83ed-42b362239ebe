import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>plete, Button, message } from 'antd';
import { UserSwitchOutlined } from '@ant-design/icons';
import { fetchUserList, switchUser } from '../services/userService';
import { decrypt, getUserInfo } from '../utils/encryption';
import { isExtensionEnvironment } from '../utils/chromeApi';
import '../styles/userSelector.css'

const UserSelector = () => {
    const [selectedUser, setSelectedUser] = useState('');
    const [options, setOptions] = useState([]);
    const [loading, setLoading] = useState(false);

    // 初始化获取用户列表
    useEffect(() => {
        // 初始化时获取当前用户信息
        if (isExtensionEnvironment()) {
            // 插件环境：通过chrome API获取页面localStorage
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                if (tabs && tabs[0] && tabs[0].id) {
                    chrome.scripting.executeScript({
                        target: { tabId: tabs[0].id },
                        function: () => {
                            const userInfo = localStorage.getItem('de0505_userInfo');
                            return userInfo;
                        }
                    }, (results) => {
                        if (chrome.runtime.lastError) {
                            console.error('获取用户信息失败:', chrome.runtime.lastError);
                            return;
                        }

                        processUserInfo(results[0]?.result);
                    });
                }
            });
        } else {
            // 本地开发环境：直接从localStorage获取
            const userInfoStr = localStorage.getItem('de0505_userInfo');
            processUserInfo(userInfoStr);
        }

        loadUserList();
    }, []);

    // 处理用户信息
    const processUserInfo = (userInfoStr) => {
        if (!userInfoStr) return;

        try {
            const userInfo = JSON.parse(userInfoStr);
            const name = userInfo.name;
            const loginName = userInfo.loginName;

            // 尝试解密
            try {
                const decryptedName = JSON.parse(decrypt(name));
                const decryptedLoginName = JSON.parse(decrypt(loginName));
                setSelectedUser(decryptedName + `(${decryptedLoginName})`);
            } catch (decryptError) {
                // 如果解密失败，可能是未加密的数据
                console.warn('解密失败，使用原始值:', decryptError);
                setSelectedUser(name + `(${loginName})`);
            }
        } catch (parseError) {
            console.error('解析用户信息失败:', parseError);
        }
    };

    // 加载用户列表
    const loadUserList = async (val = undefined) => {
        setLoading(true);
        try {
            const userOptions = await fetchUserList(val);
            setOptions(userOptions);
        } finally {
            setLoading(false);
        }
    };

    // 搜索用户
    const handleSearch = (value) => {
        loadUserList(value);
    };

    // 清除用户
    const onClear = () => {
        setSelectedUser('');
        if (isExtensionEnvironment()) {
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                chrome.scripting.executeScript({
                    target: { tabId: tabs[0].id },
                    function: () => {
                        localStorage.removeItem("de0505_userInfo");
                        return true;
                    }
                }, (results) => {
                    if (results && results[0]?.result) {
                        message.success(`已清除用户信息`);
                    } else {
                        message.error("清除失败");
                    }
                });
            });
        } else {
            localStorage.removeItem("de0505_userInfo");
            message.success(`已清除用户信息`);
        }
    };

    // 切换用户
    const handleUserSwitch = async () => {
        if (!selectedUser) {
            message.error('请选择人员');
            return;
        }
        const user = options.find(u => u.value === selectedUser);
        if (!user) {
            message.error('未找到选择的人员');
            return;
        }

        try {
            const loginInfo = await getUserInfo();
            const userInfo = {}
            userInfo.id = user.id;
            userInfo.name = user.name;
            userInfo.loginName = user.loginName;
            userInfo.idmId = user.idmid;
            userInfo.initId = loginInfo.id;
            userInfo.initIdmid = loginInfo.idmId;
            userInfo.initUserName = loginInfo.name;
            userInfo.initLoginName = loginInfo.loginName;
            await switchUser(userInfo);
        } catch (error) {
            console.error('获取用户信息失败:', error);
            message.error('获取用户信息失败: ' + error.message);
        }
    };

    return (
        <div className="section">
            <AutoComplete
                value={selectedUser}
                options={options}
                onSearch={handleSearch}
                onChange={setSelectedUser}
                placeholder="搜索或选择人员"
                style={{ width: '100%' }}
                loading={loading}
                notFoundContent={loading ? "加载中..." : "未找到匹配人员"}
                dropdownStyle={{ zIndex: 1050 }} // 增加z-index确保下拉菜单在最上层
                popupClassName='ant-select-dropdown'
                allowClear
                onClear={() => onClear()}
            />
            <Button
                type="primary"
                icon={<UserSwitchOutlined />}
                onClick={handleUserSwitch}
                style={{ marginTop: 8, width: '100%' }}
                loading={loading}
                disabled={!selectedUser}
            >
                切换人员
            </Button>
        </div>
    );
};

export default UserSelector;



