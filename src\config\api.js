// API配置文件
// 统一管理所有API相关的配置

// 根据环境判断使用哪个API地址
const isDevelopment = process.env.NODE_ENV === 'development';

// 代理服务器配置
export const API_CONFIG = {
  // 代理服务器地址 - 请根据实际情况修改
  PROXY_SERVER: 'http://10.52.70.10:410',
  
  // API前缀
  API_PREFIX: '/api-dev',
  
  // 完整的API基础地址
  get BASE_URL() {
    return `${this.PROXY_SERVER}${this.API_PREFIX}`;
  },
  
  // 具体的API端点
  ENDPOINTS: {
    // 用户相关
    GET_USER_LIST: '/qfc-business-bcc/v2/user/selector/getUserWithRecent',
    
    // 其他API端点可以在这里添加
    // DEPLOY: '/deploy/endpoint',
    // TOKEN: '/token/endpoint'
  }
};

// 获取完整的API URL
export const getApiUrl = (endpoint) => {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};

// 导出常用的API地址
export const USER_LIST_API = getApiUrl(API_CONFIG.ENDPOINTS.GET_USER_LIST);

export default API_CONFIG;
