// 内容脚本
console.log('Content script loaded');

// 检查是否启用
chrome.storage.local.get('enable', (result) => {
  const enabled = result.enable === 'true';
  if (enabled) {
    console.log('Extension is enabled on this page');
  } else {
    console.log('Extension is disabled on this page');
  }
});

// 监听来自页面的消息
window.addEventListener('message', (event) => {
  // 确保消息来自同一个窗口
  if (event.source !== window) return;
  
  if (event.data.type && event.data.type === 'FROM_PAGE') {
    console.log('Received message from page:', event.data);
  }
});