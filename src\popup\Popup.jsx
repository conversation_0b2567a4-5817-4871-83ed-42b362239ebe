import React from 'react';
import './Popup.css';
import { mockChromeAPI } from '../utils/chromeApi';
import RuleSwitch from '../components/RuleSwitch';
import UserSelector from '../components/UserSelector';
import TokenButtons from '../components/TokenButtons';
import DeployButton from '../components/DeployButton';

// 在组件外部调用模拟函数
mockChromeAPI();

const Popup = () => {
  return (
    <div className="popup-container">
      <RuleSwitch />
      <UserSelector />
      <TokenButtons />
      <DeployButton />
    </div>
  );
};

export default Popup;

