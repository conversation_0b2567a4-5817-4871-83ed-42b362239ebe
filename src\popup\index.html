<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FAW Mock Extension</title>
  <script>
    // 在页面加载前模拟Chrome API
    if (typeof chrome === 'undefined' || !chrome.storage || !chrome.storage.local) {
      window.chrome = {
        storage: {
          local: {
            get: (key, callback) => {
              const value = localStorage.getItem(key);
              callback({ [key]: value });
            },
            set: (obj, callback) => {
              Object.keys(obj).forEach(key => {
                localStorage.setItem(key, obj[key]);
              });
              if (callback) callback();
            }
          }
        },
        tabs: {
          query: (queryInfo, callback) => {
            callback([{ id: 1, url: window.location.href }]);
          },
          create: ({ url }) => {
            window.open(url, '_blank');
          }
        },
        scripting: {
          executeScript: ({ target, function: func, args=[] }, callback) => {
            try {
              const result = func(...args);
              callback([{ result }]);
            } catch (error) {
              console.error('模拟executeScript出错:', error);
              callback([{ error: error.message }]);
            }
          }
        },
        runtime: {
          getURL: (path) => `/${path}`,
          lastError: null
        }
      };
      console.log('已模拟Chrome API');
    }
  </script>
</head>
<body>
  <div id="root"></div>
</body>
</html>


