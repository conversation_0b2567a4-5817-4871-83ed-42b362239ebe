import { message } from 'antd';
import { getToken, isExtensionEnvironment } from '../utils/chromeApi';

// 去部署
export const deploy = async () => {
  try {
    const token = await getToken("jwt");
    if (!token) {
      message.error('获取TOKEN失败');
      return false;
    }

    if (!isExtensionEnvironment()) {
      // 在开发环境中模拟部署
      window.open(`/deploy/index.html?token=Bearer ${token}`, '_blank');
      return true;
    }

    // 获取扩展的URL
    const extensionUrl = chrome.runtime.getURL('deploy/index.html');

    // 创建新标签页并打开部署页面
    chrome.tabs.create({
      url: `${extensionUrl}?token=${token}`,
    });
    return true;
  } catch (error) {
    console.error('部署失败:', error);
    message.error('部署失败');
    return false;
  }
};

// 去部署 - 备选方法
export const deployAlternative = async () => {
  try {
    const token = await getToken("jwt");
    if (!token) {
      message.error('获取TOKEN失败');
      return false;
    }

    // 直接使用相对路径
    chrome.tabs.create({
      url: chrome.runtime.getURL(`deploy/index.html?token=Bearer ${token}`),
    });
    return true;
  } catch (error) {
    console.error('部署失败:', error);
    message.error('部署失败');
    return false;
  }
};

// 去部署 - 外部URL
export const deployExternal = async () => {
  try {
    const token = await getToken("jwt");
    if (!token) {
      message.error('获取TOKEN失败');
      return false;
    }

    // 使用外部URL
    chrome.tabs.create({
      url: `https://your-server.com/deploy/index.html?token=Bearer ${token}`,
    });
    return true;
  } catch (error) {
    console.error('部署失败:', error);
    message.error('部署失败');
    return false;
  }
};