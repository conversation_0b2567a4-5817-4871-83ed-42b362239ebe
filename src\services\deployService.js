import { message } from 'antd';
import { getTokenFromFAW, isExtensionEnvironment } from '../utils/chromeApi';

// 去部署
export const deploy = async () => {
  try {
    // 从FAW域名的标签页获取token
    const token = await getTokenFromFAW("jwt");
    if (!token) {
      message.error('获取TOKEN失败');
      return false;
    }

    if (!isExtensionEnvironment()) {
      // 在开发环境中模拟部署
      window.open(`/deploy/index.html?token=Bearer ${token}`, '_blank');
      return true;
    }

    // 获取扩展的URL
    const extensionUrl = chrome.runtime.getURL('deploy/index.html');

    // 创建新标签页并打开部署页面
    chrome.tabs.create({
      url: `${extensionUrl}?token=Bearer ${token}`,
    });
    return true;
  } catch (error) {
    console.error('部署失败:', error);
    message.error('部署失败');
    return false;
  }
};
