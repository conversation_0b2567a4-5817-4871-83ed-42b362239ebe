import { message } from 'antd';
import { getToken } from '../utils/chromeApi';

// 复制TOKEN
export const copyToken = async () => {
  try {
    const jwt = await getToken("jwt");
    if (!jwt) {
      message.error('获取TOKEN失败');
      return false;
    }

    await navigator.clipboard.writeText(`Bearer ${jwt}`);
    message.success('TOKEN已复制到剪贴板');
    return true;
  } catch (error) {
    console.error('复制TOKEN失败:', error);
    message.error('复制TOKEN失败');
    return false;
  }
};

// 复制SIT URL
export const copySitUrl = async () => {
  try {
    const jwt = await getToken("jwt");
    if (!jwt) {
      message.error('获取TOKEN失败');
      return false;
    }

    // const sitUrl = `https://sit-iwork.faw.cn?token=${jwt}&userId=1471301459636428802&source=zerotrust&tenantId=YQJT&systemId=BA-0222&loginName=U2FsdGVkX19Zdsxql/rGIqB/zwd2fyNU780vX+nZEyM=&shortName=fcw&workbenchGroupCode=WB007&roleGroupCode=fcw&checkIp=&cnName=%25E6%259D%259C%25E5%25B0%2594%25E4%25BB%2598&idmId=u2019272664&encrypt=U2FsdGVkX18qal+wv0x1Kg1uS5CEwYs7Ea/5a7FcquxIEWWZNhiHJSZis+TXUhjCXeD+zd73BQTHyjocepuX/yFFsLOSyZ5kEj7ArpBntgRS9/AEQqsRWlE+xkTXA8wMpvaVF3Ad7NwLhRbVc1imd4wfDGSjsBLesv+6LS533yU=`;
    const sitUrl = `https://sit-iwork.faw.cn/?
    token=${jwt}
    &userId=1471301459636428802
    &source=zerotrust
    &tenantId=YQJT
    &systemId=BA-0222
    &loginName=U2FsdGVkX19Zdsxql/rGIqB/zwd2fyNU780vX+nZEyM=
    &shortName=fcw
    &workbenchGroupCode=WB007
    &roleGroupCode=fcw
    &checkIp=
    &cnName=%25E6%259D%259C%25E5%25B0%2594%25E4%25BB%2598
    &idmId=u2019272664
    &encrypt=U2FsdGVkX18qal+wv0x1Kg1uS5CEwYs7Ea/5a7FcquxIEWWZNhiHJSZis+TXUhjCXeD+zd73BQTHyjocepuX/yFFsLOSyZ5kEj7ArpBntgRS9/AEQqsRWlE+xkTXA8wMpvaVF3Ad7NwLhRbVc1imd4wfDGSjsBLesv+6LS533yU=`
    await navigator.clipboard.writeText(sitUrl);
    message.success('SIT URL已复制到剪贴板');
    return true;
  } catch (error) {
    console.error('复制SIT URL失败:', error);
    message.error('复制SIT URL失败');
    return false;
  }
};