import { message } from 'antd';
import { getToken, isExtensionEnvironment } from '../utils/chromeApi';
import { setNewUserInfo } from '../utils/encryption';
import { $post } from '../utils/fetch';

// 从API获取用户列表
export const fetchUserList = async (search) => {
  try {
    const jwt = await getToken("jwt");
    
    // 在扩展环境中，通过background脚本发送请求
    if (isExtensionEnvironment()) {
      return new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({
          type: 'FETCH_USER_LIST',
          payload: {
            jwt: 'Bearer '+jwt,
            search: search
          }
        }, response => {
          if (response.error) {
            message.error(response.error);
            reject(new Error(response.error));
          } else {
            resolve(response.data);
          }
        });
      });
    }
    
    // 在开发环境中直接发送请求
    const response = await fetch('/api-dev/qfc-business-bcc/v2/user/selector/getUserWithRecent', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${jwt}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        search: search,
        pageSize: 6,
        requestDataObject: {},
        requestDataType: "NORMAL",
        rootCode: "CUSTOM_TASK"
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      if (data && Array.isArray(data.data)) {
        // 转换API返回的数据格式为AutoComplete需要的格式
        return data.data.map(user => ({
          ...user,
          value: user.name + `(${user.loginName})`,
          label: user.name + `(${user.loginName})`
        }));
      } else {
        message.warning('获取用户列表失败，使用默认数据');
        return [];
      }
    } else {
      console.error('获取用户列表失败:', response.statusText);
      message.error('获取用户列表失败');
      return [];
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    message.error('获取用户列表失败');
    return [];
  }
};

// 切换用户
export const switchUser = async (user) => {
  if (!user) return false;

  if (!isExtensionEnvironment()) {
    // 在扩展环境中模拟切换用户
    localStorage.setItem("de0505_userInfo", JSON.stringify(setNewUserInfo(user)));
    message.success(`已切换到 ${user.name}`); 
    return true;
  }

  return new Promise((resolve) => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      chrome.scripting.executeScript({
        target: { tabId: tabs[0].id },
        function: (userData) => {
          localStorage.setItem("de0505_userInfo", JSON.stringify(userData));
          return true;
        },
        args: [setNewUserInfo(user)]
      }, (results) => {
        if (results && results[0]?.result) {
          message.success(`已切换到 ${user.name}`);
          resolve(true);
        } else {
          message.error("切换失败");
          resolve(false);
        }
      });
    });
  });
};












