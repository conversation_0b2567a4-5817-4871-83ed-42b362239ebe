
/**
 * @function 判断是否在扩展环境中运行
 * @description 用于判断当前代码是否在Chrome扩展的环境中运行
 * @returns {Boolean} 是否在扩展环境中运行
 */
export const isExtensionEnvironment = () => {
  // 检查多个条件来确定是否在扩展环境中运行
  // 1. 检查chrome API是否存在
  const hasChromeAPI = typeof chrome !== 'undefined';
  
  // 2. 检查chrome.extension API是否存在（扩展特有）
  const hasExtensionAPI = hasChromeAPI && typeof chrome.extension !== 'undefined';
  
  // 3. 检查chrome.runtime API是否存在（扩展特有）
  const hasRuntimeAPI = hasChromeAPI && typeof chrome.runtime !== 'undefined';
  
  // 4. 检查chrome.runtime.id是否存在（扩展特有）
  const hasExtensionId = hasRuntimeAPI && typeof chrome.runtime.id === 'string';
  
  // 5. 检查当前URL是否以chrome-extension://开头
  const isExtensionProtocol = window.location.protocol === 'chrome-extension:';
  
  // 6. 检查是否能访问扩展特有的API
  // const hasScriptingAPI = hasChromeAPI && typeof chrome.scripting !== 'undefined';
  
  // 综合判断：满足任一条件即可认为在扩展环境中
//   return hasExtensionAPI || hasExtensionId || isExtensionProtocol || hasScriptingAPI;
  return hasExtensionAPI || hasExtensionId || isExtensionProtocol;
};

// 模拟Chrome API
export const mockChromeAPI = () => {
  if (!isExtensionEnvironment()) {
    console.log('在非扩展环境中运行，使用模拟的Chrome API');
    window.chrome = {
      ...window.chrome,
      storage: {
        local: {
          get: (key, callback) => {
            const value = localStorage.getItem(key);
            callback({ [key]: value });
          },
          set: (obj, callback) => {
            Object.keys(obj).forEach(key => {
              localStorage.setItem(key, obj[key]);
            });
            if (callback) callback();
          }
        }
      },
      tabs: {
        query: (queryInfo, callback) => {
          callback([{ id: 1, url: window.location.href }]);
        },
        create: ({ url }) => {
          window.open(url, '_blank');
        }
      },
      scripting: {
        executeScript: ({ target, function: func, args=[] }, callback) => {
          try {
            const result = func(...args);
            callback([{ result }]);
          } catch (error) {
            console.error('模拟executeScript出错:', error);
            callback([{ error: error.message }]);
          }
        }
      },
      runtime: {
        getURL: (path) => `/${path}`,
        lastError: null
      }
    };
  }
};

// 获取token
export const getToken = async (key) => {
  // 在扩展环境中返回模拟token
  if (!isExtensionEnvironment()) {
    console.log('扩展环境中使用模拟token');
    return 'eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJkdWVyZnUiLCJjcmVhdGVkIjoxNzUwMjMzMzA5NTY3LCJpZG1pZCI6InUyMDE5MjcyNjY0IiwiZXhwIjoxNzUwODM4MTA5LCJ1cGtpZCI6IjE0NzEzMDE0NTk2MzY0Mjg4MDIifQ.6_ZbHzCNCR6txK_Cbp3Awc83UYb3iTPkAUTUYEqPFA8D4zrBtrtWg5wFNMQt0XtyAi_6M6PmAhMJ_7mCIk9esA';
  }

  return new Promise((resolve, reject) => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (!tabs || !tabs[0] || !tabs[0].id) {
        reject(new Error('无法获取当前标签页'));
        return;
      }

      chrome.scripting.executeScript({
        target: { tabId: tabs[0].id },
        function: (tokenKey) => {
          const token = localStorage.getItem(tokenKey);
          return (JSON.parse(token)?.value || '');
        },
        args: [key]
      }, (results) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(results[0]?.result || '');
        }
      });
    });
  });
};

// 从FAW域名的标签页获取token
export const getTokenFromFAW = async (key = 'jwt') => {
  if (!isExtensionEnvironment()) {
    console.log('开发环境中使用模拟token');
    return 'eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJkdWVyZnUiLCJjcmVhdGVkIjoxNzUwMjMzMzA5NTY3LCJpZG1pZCI6InUyMDE5MjcyNjY0IiwiZXhwIjoxNzUwODM4MTA5LCJ1cGtpZCI6IjE0NzEzMDE0NTk2MzY0Mjg4MDIifQ.6_ZbHzCNCR6txK_Cbp3Awc83UYb3iTPkAUTUYEqPFA8D4zrBtrtWg5wFNMQt0XtyAi_6M6PmAhMJ_7mCIk9esA';
  }

  return new Promise((resolve, reject) => {
    // 查找所有包含 https://iwork.faw.cn 的标签页
    chrome.tabs.query({ url: 'https://iwork.faw.cn/*' }, (tabs) => {
      if (chrome.runtime.lastError) {
        reject(new Error('查询FAW标签页失败: ' + chrome.runtime.lastError.message));
        return;
      }

      if (!tabs || tabs.length === 0) {
        reject(new Error('未找到 https://iwork.faw.cn 的标签页，请先打开该页面'));
        return;
      }

      // 使用第一个找到的FAW标签页
      const fawTab = tabs[0];

      chrome.scripting.executeScript({
        target: { tabId: fawTab.id },
        function: (tokenKey) => {
          const token = localStorage.getItem(tokenKey);
          if (!token) {
            return null;
          }
          try {
            const parsed = JSON.parse(token);
            return parsed?.value || token;
          } catch (e) {
            // 如果不是JSON格式，直接返回原值
            return token;
          }
        },
        args: [key]
      }, (results) => {
        if (chrome.runtime.lastError) {
          reject(new Error('从FAW页面获取token失败: ' + chrome.runtime.lastError.message));
        } else {
          const token = results[0]?.result;
          if (!token) {
            reject(new Error('在FAW页面中未找到token，请确保已登录'));
          } else {
            resolve(token);
          }
        }
      });
    });
  });
};
