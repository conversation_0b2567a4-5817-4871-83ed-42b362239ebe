import CryptoJS from 'crypto-js';
import { isExtensionEnvironment } from './chromeApi';

const SECRET_KEY = 'iwork' || 'default-secret-key';

export const encrypt = (value) => {
  return CryptoJS.AES.encrypt(value, SECRET_KEY).toString();
};

export const decrypt = (value) => {
  const bytes = CryptoJS.AES.decrypt(value, SECRET_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
};

export const setNewUserInfo = (val) => {
  const target = {};
  for (const [key, value] of Object.entries(val)) {
    target[key] = encrypt(JSON.stringify(value));
  }
  return target;
};

// 获取用户信息
export const getUserInfo = () => {
  return new Promise((resolve, reject) => {
    if (isExtensionEnvironment()) {
      // 插件环境：通过chrome API获取页面localStorage
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (!tabs || !tabs[0] || !tabs[0].id) {
          reject(new Error('无法获取当前标签页'));
          return;
        }
        
        chrome.scripting.executeScript({
          target: { tabId: tabs[0].id },
          function: () => {
            const userInfoStr = localStorage.getItem('user_info');
            return userInfoStr;
          }
        }, (results) => {
          if (chrome.runtime.lastError) {
            reject(new Error(`获取用户信息失败: ${chrome.runtime.lastError.message}`));
            return;
          }
          
          const userInfoStr = results[0]?.result;
          if (!userInfoStr) {
            reject(new Error('未找到用户信息'));
            return;
          }
          
          try {
            const userInfo = JSON.parse(userInfoStr);
            
            // 尝试解密用户信息
            const decryptedInfo = {};
            for (const [key, value] of Object.entries(userInfo)) {
              try {
                // 尝试解密
                const decrypted = decrypt(value);
                decryptedInfo[key] = JSON.parse(decrypted);
              } catch (e) {
                // 解密失败，使用原值
                decryptedInfo[key] = value;
              }
            }
            
            resolve(decryptedInfo);
          } catch (error) {
            reject(new Error(`解析用户信息失败: ${error.message}`));
          }
        });
      });
    } else {
      // 本地开发环境：直接从localStorage获取
      const userInfoStr = localStorage.getItem('user_info') || '{}' ;
      if (!userInfoStr) {
        reject(new Error('未找到用户信息'));
        return;
      }
      
      try {
        const userInfo = JSON.parse(userInfoStr);
        
        // 尝试解密用户信息
        const decryptedInfo = {};
        for (const [key, value] of Object.entries(userInfo)) {
          try {
            // 尝试解密
            const decrypted = decrypt(value);
            decryptedInfo[key] = JSON.parse(decrypted);
          } catch (e) {
            // 解密失败，使用原值
            decryptedInfo[key] = value;
          }
        }
        
        resolve(decryptedInfo);
      } catch (error) {
        reject(new Error(`解析用户信息失败: ${error.message}`));
      }
    }
  });
};
