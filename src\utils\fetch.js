
// 使用代理服务器地址替代本地nginx
export const API_PREFIX = "http://10.52.70.10:410";

export const $fetch = async (api, options = {}) => {
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get("token");
  if (!token) {
    return;
  }
  // 设置超时
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 60 * 1000); // 1分钟
  try {
    const response = await fetch(api, {
      ...options,
      headers: {
        Authorization: `Bearer ${token}`,
        ...options.headers,
      },
      signal: controller.signal,
    });
    const { status } = response;
    if (status === 200) {
      return response.json();
    }
    // 只有200才算作成功，其它都当作异常
    throw new Error(`${status}`);
  } catch (error) {
    return error;
  } finally {
    clearTimeout(timeoutId);
  }
};

export const $get = (api, params = {}) => {
  const queryArr = Object.entries(params).map(([k, v]) => `${k}=${v}`);
  const queryStr = queryArr.length ? `?${queryArr.join("&")}` : "";
  return $fetch(`${api}${queryStr}`);
};

export const $post = (api, params = {}) => {
  return $fetch(`${api}`, {
    method: "POST",
    body: JSON.stringify(params),
    headers: {
      "Content-Type": "application/json",
    },
  });
};