// 校验url
export const checkUrl = async (reg, url) => {
  if (!reg) {
    return false;
  }
  return new Promise((resolve, reject) => {
    if (url) {
      resolve(reg instanceof RegExp ? reg.test(url) : reg.includes(url));
    }
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      }
      const url = tabs[0]?.url;
      resolve(reg instanceof RegExp ? reg.test(url) : reg.includes(url));
    });
  });
};

export const getToken = async (tokenKey) => {
  return new Promise((resolve, reject) => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      chrome.scripting.executeScript(
        {
          target: { tabId: tabs[0].id },
          function: (key) => localStorage.getItem(key),
          args: [tokenKey],
        },
        (results) => {
          if (results?.[0]?.result) {
            const jwt = JSON.parse(results[0].result)?.value;
            jwt ? resolve(jwt) : reject("JWT not valid.");
          } else {
            reject("JWT not found in localStorage.");
          }
        }
      );
    });
  });
};

export const loadStatic = async (path) => {
  const res = await fetch(chrome.runtime.getURL(path));
  return await res.json();
};

export const formatDate = (timestamp) => {
  if (!timestamp) {
    return `-`;
  }
  const date = new Date(timestamp);
  const year = date.getFullYear(); // 2025
  const month = (date.getMonth() + 1).toString().padStart(2, "0"); // 04（月份从0开始，需+1）
  const day = date.getDate().toString().padStart(2, "0"); // 23
  const hours = date.getHours().toString().padStart(2, "0"); // 13
  const minutes = date.getMinutes().toString().padStart(2, "0"); // 43
  const seconds = date.getSeconds().toString().padStart(2, "0"); // 09

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

export class LoadingSpinner {
  constructor(selector, interval = 200) {
    this.selector = selector;
    this.interval = interval;
    this.loadingStrMap = ["-", "\\", "|", "/"];
    this.loadingStrLen = this.loadingStrMap.length;
    this.loadingStrIndex = 0;
    this.timer = null;
  }

  start() {
    if (!this.selector) return;
    if (this.timer) return;
    this.timer = window.setInterval(() => {
      const loadingStr = document.querySelector(this.selector);
      if (loadingStr) {
        loadingStr.innerHTML = this.loadingStrMap[this.loadingStrIndex];
        if (this.loadingStrIndex === this.loadingStrLen - 1) {
          this.loadingStrIndex = 0;
        } else {
          this.loadingStrIndex += 1;
        }
      }
    }, this.interval);
  }

  end() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
      this.loadingStrIndex = 0;
    }
  }
}

export class TimeIntervalUpdater {
  constructor(selector, interval = 1000) {
    this.selector = selector;
    this.interval = interval;
    this.timer = null;
  }

  calculateTime(startTime, endTime) {
    const prev = new Date(startTime);
    const current = endTime ? new Date(endTime) : new Date();
    const diff = Math.floor((current - prev) / 1000);
    const hours = Math.floor(diff / 3600);
    const minutes = Math.floor((diff % 3600) / 60);
    const seconds = diff % 60;
    return {
      hours,
      minutes,
      seconds,
    };
  }

  start(startTime = null) {
    if (!this.selector) return;
    if (this.timer) return;
    if (startTime) this.startTime = startTime;
    this.timer = window.setInterval(() => {
      const { hours, minutes, seconds } = this.calculateTime(this.startTime);
      const timeIntervalStr = document.querySelector(this.selector);
      if (timeIntervalStr) {
        timeIntervalStr.innerText = `已进行 ${hours} 时 ${minutes} 分 ${seconds} 秒`;
      }
    }, this.interval);
  }

  end() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }
}
