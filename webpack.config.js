const path = require('path');
const CopyPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';
  
  const config = {
    mode: isProduction ? 'production' : 'development',
    devtool: isProduction ? 'source-map' : 'inline-source-map',
    entry: {
      popup: './src/popup/index.jsx',
      background: './src/background.js',
      contentScript: './src/contentScript.js'
    },
    module: {
      rules: [
        {
          test: /\.(js|jsx)$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env', '@babel/preset-react']
            }
          }
        },
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader']
        }
      ]
    },
    plugins: [
      new CopyPlugin({
        patterns: [
          { from: 'public' },
          { from: 'deploy', to: 'deploy' },
          { from: 'utils', to: 'utils' }
        ]
      }),
      new HtmlWebpackPlugin({
        template: './src/popup/index.html',
        filename: 'popup.html',
        chunks: ['popup']
      })
    ],
    resolve: {
      extensions: ['.js', '.jsx']
    },
    output: {
      filename: '[name].js',
      path: path.resolve(__dirname, 'dist')
    }
  };
  
  // 只在开发模式下添加devServer配置
  if (!isProduction) {
    config.devServer = {
      static: path.join(__dirname, 'dist'),
      port: 8080,
      hot: true,
      proxy: [
        {
          context: ['/api-dev'], // 要代理的请求路径前缀
          target: 'http://10.52.70.10:3000', // 代理不用调整，访问的是 前端服务器
          changeOrigin: true,
          pathRewrite: { '^/api-dev': '/api-dev' } // 可选，根据实际后端需要
        }
      ]
    };
  }
  return config;
};





